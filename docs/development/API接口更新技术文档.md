# API接口更新技术文档

## 修改概述

**目标**: 使用老板提供的正确API接口 `/permission/getUserRemainingDays` 获取用户剩余试用天数
**修改文件**: 
- `src/views/user_front/InzUserFront.api.ts`
- `src/views/user_front/components/ExperiencePermissionModal.vue`
**完成时间**: 2025-08-01

## API接口更新

### 1. 接口变更

**修改前**:
```typescript
// 获取用户试用使用情况
export const getUserTrialStatus = (params: { userId: string }) => {
  return defHttp.get({ 
    url: '/user_front/inzUserFront/getUserTrialStatus', 
    params 
  });
};
```

**修改后**:
```typescript
// 获取用户剩余试用天数
export const getUserRemainingDays = (params: { userId: string }) => {
  return defHttp.get({ 
    url: '/permission/getUserRemainingDays', 
    params 
  });
};
```

### 2. 接口规格

**接口地址**: `GET /permission/getUserRemainingDays`
**请求参数**: 
```typescript
{
  userId: string  // 用户ID
}
```

**返回数据**:
```typescript
{
  success: boolean;
  result: number;  // 直接返回剩余天数，如：3
}
```

**使用示例**:
```javascript
// 获取剩余天数
const response = await getUserRemainingDays({ userId: 'user123' });
const remainingDays = response.result; // 返回数字，如：3
```

## 组件逻辑更新

### 1. 导入更新

**修改位置**: 第148行
```javascript
// 修改前
import { getEducationList, getConfig, getUserTrialStatus } from '../InzUserFront.api';

// 修改后
import { getEducationList, getConfig, getUserRemainingDays } from '../InzUserFront.api';
```

### 2. 新增响应式变量

**修改位置**: 第163-167行
```javascript
const userStore = useUserStore();
const defaultDays = ref(8); // 默认天数，将从配置中动态获取
const maxTrialDays = ref(8); // 最大试用天数限制，将从配置中动态获取
const userUsedTrialDays = ref(0); // 用户已使用的试用天数
const userRemainingDays = ref(8); // 用户剩余试用天数（从API直接获取）
```

### 3. 计算逻辑简化

**修改位置**: 第195-198行
```javascript
// 修改前 - 复杂计算
const getRemainingTrialDays = computed(() => {
  return Math.max(0, maxTrialDays.value - userUsedTrialDays.value);
});

// 修改后 - 直接使用API返回值
const getRemainingTrialDays = computed(() => {
  return userRemainingDays.value;
});
```

### 4. API调用逻辑更新

**修改位置**: 第324-371行
```javascript
// 加载用户剩余试用天数
const loadTrialStatus = async () => {
  if (!selectedUser.value?.id) return;

  try {
    // 调用API获取用户剩余试用天数
    const res = await getUserRemainingDays({ userId: selectedUser.value.id });
    
    if (res && res.success && typeof res.result === 'number') {
      // 直接获取剩余天数
      const remainingDays = res.result;
      userRemainingDays.value = remainingDays;
      
      // 计算已使用天数 = 总配置天数 - 剩余天数
      userUsedTrialDays.value = Math.max(0, maxTrialDays.value - remainingDays);
      
      console.log(`用户 ${selectedUser.value.realName} 剩余试用天数: ${remainingDays}`);
      console.log(`计算得出已使用天数: ${userUsedTrialDays.value}`);
      
      // 更新试用状态信息
      trialStatus.value = {
        hasActiveTrial: remainingDays > 0,
        remainingDays: remainingDays,
        usedTrialDays: userUsedTrialDays.value,
        totalTrialDays: maxTrialDays.value
      };
    } else {
      console.warn('获取用户剩余试用天数失败，使用默认值');
      userRemainingDays.value = maxTrialDays.value; // 使用总配置天数作为默认值
      userUsedTrialDays.value = 0;
    }

    console.log(
      '试用状态加载完成 - maxTrialDays:',
      maxTrialDays.value,
      'userUsedTrialDays:',
      userUsedTrialDays.value,
      'remainingDays:',
      getRemainingTrialDays.value
    );
  } catch (error) {
    console.error('获取用户剩余试用天数失败:', error);
    // API调用失败时，使用默认值
    userRemainingDays.value = maxTrialDays.value; // 使用总配置天数作为默认值
    userUsedTrialDays.value = 0;
    console.log('API调用失败，使用默认值：剩余天数等于总配置天数');
  }
};
```

## 数据流程优化

### 1. 数据获取流程
```
1. 用户选择 → 打开弹窗
2. 加载配置 → 获取总试用天数（8天）
3. 调用API → 获取剩余天数（3天）
4. 计算已用 → 已使用天数 = 8 - 3 = 5天
5. 设置默认值 → 表单显示剩余天数（3天）
```

### 2. 数据来源明确
- **总配置天数**: 从配置 `FREE_MEMBERSHIP_DAYS` 获取
- **剩余天数**: 从API `/permission/getUserRemainingDays` 直接获取
- **已使用天数**: 通过计算得出（总天数 - 剩余天数）

### 3. 错误处理机制
- **API调用成功**: 使用返回的剩余天数
- **API返回异常**: 使用总配置天数作为默认值
- **网络异常**: 使用总配置天数作为默认值

## 业务逻辑改进

### 1. 准确性提升
- **直接获取**: 不再依赖复杂的计算逻辑
- **实时数据**: 每次打开弹窗都获取最新的剩余天数
- **数据一致**: 确保前端显示与后端数据一致

### 2. 用户体验优化
- **即时反馈**: 打开弹窗立即显示正确的剩余天数
- **防错机制**: 最大值限制为剩余天数，避免超额配置
- **状态同步**: 试用状态信息实时更新

### 3. 性能优化
- **简化计算**: 减少前端复杂计算逻辑
- **缓存机制**: 可在组件级别缓存剩余天数
- **错误恢复**: 优雅的降级处理机制

## 测试验证

### 1. API接口测试
```javascript
// 测试用例1：正常获取剩余天数
const result1 = await getUserRemainingDays({ userId: 'user123' });
// 期望: { success: true, result: 3 }

// 测试用例2：新用户（未使用试用）
const result2 = await getUserRemainingDays({ userId: 'newUser' });
// 期望: { success: true, result: 8 }

// 测试用例3：试用已用完
const result3 = await getUserRemainingDays({ userId: 'expiredUser' });
// 期望: { success: true, result: 0 }
```

### 2. 界面显示测试
- [ ] 新用户显示8天剩余
- [ ] 已用5天用户显示3天剩余
- [ ] 用完试用用户显示0天
- [ ] API失败时显示默认值

### 3. 业务逻辑测试
- [ ] 输入框最大值限制正确
- [ ] 费用计算准确
- [ ] 权限类型切换正常
- [ ] 表单验证有效

## 部署注意事项

### 1. 后端接口确认
- 确保 `/permission/getUserRemainingDays` 接口已部署
- 验证接口返回数据格式正确
- 测试不同用户的剩余天数数据

### 2. 数据一致性
- 确保后端计算逻辑正确
- 验证剩余天数的实时性
- 检查并发操作的数据一致性

### 3. 错误处理
- 测试网络异常情况
- 验证权限控制正确
- 确认降级处理机制

## 后续优化建议

### 1. 缓存机制
- 实现组件级别的数据缓存
- 添加数据过期时间控制
- 支持手动刷新数据

### 2. 实时同步
- 考虑WebSocket实时更新
- 添加数据变更通知
- 实现多标签页数据同步

### 3. 用户体验
- 添加加载状态提示
- 优化错误提示信息
- 支持数据预加载

---

**修改完成状态**: ✅ 已完成
**API接口**: 已更新为正确的 `/permission/getUserRemainingDays`
**数据流程**: 直接获取剩余天数，简化计算逻辑
**用户体验**: 准确显示剩余可配置天数
