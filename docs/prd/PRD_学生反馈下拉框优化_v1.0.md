# 学生反馈下拉框优化 PRD

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-02
- **负责人**: Emma (产品经理)
- **项目**: 学生反馈功能优化

## 2. 背景与问题陈述

### 当前问题
1. **数据显示异常**: 学生反馈按钮无法正确显示学生数据，而用户信息行可以正常显示
2. **显示内容错误**: 下拉框显示的是学生ID而不是学生姓名
3. **用户体验差**: 缺少可输入筛选的搜索功能，用户无法快速找到目标学生

### 业务影响
- 教练无法高效地为学生提供反馈
- 用户体验差，操作效率低
- 可能导致反馈错误（选错学生）

## 3. 目标与成功指标

### 项目目标 (Objectives)
1. 修复学生反馈下拉框数据显示问题
2. 实现学生姓名正确显示
3. 添加可输入筛选的搜索功能

### 关键结果 (Key Results)
1. 学生反馈下拉框数据加载成功率达到100%
2. 下拉框显示学生姓名而非ID
3. 支持按姓名和电话号码进行模糊搜索
4. 搜索响应时间 < 500ms

### 反向指标 (Counter Metrics)
- 不能影响现有的用户信息行显示功能
- 不能降低整体页面加载性能

## 4. 用户画像与用户故事

### 目标用户
- **主要用户**: 教练
- **使用场景**: 为班级学生提供实时反馈

### 用户故事
```
作为一名教练
我希望能够快速搜索并选择学生
以便为他们提供及时的反馈

作为一名教练  
我希望在下拉框中看到学生的姓名而不是ID
以便准确识别学生身份

作为一名教练
我希望能够通过输入姓名或电话快速筛选学生
以便在学生较多时快速找到目标学生
```

## 5. 功能规格详述

### 5.1 学生下拉框优化

#### 功能描述
优化学生反馈模态框中的学生选择下拉框，实现正确的数据显示和搜索功能。

#### 具体要求
1. **数据显示**:
   - 下拉框选项显示学生姓名
   - 选项详情显示：姓名、电话、加入时间
   - 选中后显示完整学生信息

2. **搜索功能**:
   - 支持按学生姓名模糊搜索
   - 支持按电话号码模糊搜索
   - 实时搜索，无需点击搜索按钮
   - 搜索结果高亮显示匹配内容

3. **数据结构**:
   ```typescript
   interface StudentOption {
     label: string;        // 学生姓名
     value: string;        // 记录ID
     studentName: string;  // 学生姓名
     studentPhone: string; // 学生电话
     joinTime: string;     // 加入时间
     feedback?: string;    // 当前反馈
     studentId: string;    // 学生用户ID
   }
   ```

### 5.2 API接口优化

#### getClassStudent接口
- **路径**: `/inz_coach_class_student/inzCoachClassStudent/getClassStudent`
- **参数**: `{ userId: string }`
- **返回**: 当前教练管辖班级的所有学生列表
- **数据格式**: 确保返回完整的学生信息

### 5.3 用户交互流程

1. **打开反馈模态框**:
   - 点击"学生反馈"按钮或行操作"反馈"
   - 自动加载学生列表
   - 如果是行操作，自动选中对应学生

2. **搜索学生**:
   - 在下拉框中输入搜索关键词
   - 实时过滤显示匹配的学生
   - 支持姓名和电话号码搜索

3. **选择学生**:
   - 点击选择学生
   - 显示学生详细信息
   - 如果学生已有反馈，自动填充到反馈内容框

## 6. 范围定义

### 包含功能 (In Scope)
- 修复学生反馈下拉框数据加载问题
- 优化下拉框显示内容（显示姓名而非ID）
- 实现可输入筛选的搜索功能
- 优化用户交互体验

### 排除功能 (Out of Scope)
- 不修改现有的用户信息行显示逻辑
- 不涉及反馈内容的格式变更
- 不修改权限控制逻辑

## 7. 依赖与风险

### 内部依赖
- `getClassStudent` API接口正常工作
- 用户权限系统正常
- 前端组件库支持

### 外部依赖
- 后端数据库学生数据完整性
- 网络连接稳定性

### 潜在风险
1. **数据一致性风险**: 学生数据可能不完整
2. **性能风险**: 学生数量过多时搜索性能
3. **兼容性风险**: 现有功能可能受影响

### 风险缓解措施
1. 添加数据验证和错误处理
2. 实现分页或虚拟滚动
3. 充分测试现有功能

## 8. 发布初步计划

### 开发阶段
1. **Phase 1**: 修复数据加载问题（1天）
2. **Phase 2**: 优化显示内容和搜索功能（1天）
3. **Phase 3**: 测试和优化（0.5天）

### 测试计划
- 单元测试：组件功能测试
- 集成测试：API接口测试
- 用户测试：教练使用场景测试

### 上线计划
- 灰度发布：先在测试环境验证
- 全量发布：确认无问题后全量上线
- 数据监控：监控API调用成功率和响应时间

## 9. 验收标准

### 功能验收
- [ ] 学生反馈下拉框能正确加载学生数据
- [ ] 下拉框显示学生姓名而非ID
- [ ] 支持按姓名和电话搜索学生
- [ ] 搜索功能响应及时（<500ms）
- [ ] 选中学生后正确显示学生信息
- [ ] 现有功能不受影响

### 性能验收
- [ ] 页面加载时间不增加
- [ ] 搜索响应时间 < 500ms
- [ ] 支持至少1000个学生的数据量

### 用户体验验收
- [ ] 操作流程顺畅
- [ ] 错误提示友好
- [ ] 界面美观一致
