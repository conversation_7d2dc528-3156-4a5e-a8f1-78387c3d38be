# 用户表接口集成需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2025-08-01
- **负责人**: Emma (产品经理)
- **项目**: 用户表直接使用后端/list接口

## 2. 背景与问题陈述

### 2.1 当前状况
- 项目中存在用户前台管理模块 (`src/views/user_front/`)
- 现有用户表使用的是 `/user_front/inzUserFront/list` 接口
- 老板提供了新的后端接口，包含权限控制和用户信息返回功能

### 2.2 核心问题
- 需要将用户表直接集成老板提供的后端 `/list` 接口
- 避免重复调试状态，直接使用现有接口的完整功能
- 确保权限控制（系统管理员、代理商用户）正常工作

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
1. **接口集成**: 用户表直接使用老板提供的 `/list` 接口
2. **权限保持**: 保持现有的角色权限控制功能
3. **数据完整**: 确保返回的用户信息和金豆统计正常显示
4. **无缝切换**: 用户界面保持一致，无需重新调试

### 3.2 关键结果 (Key Results)
- 用户表成功调用新的 `/list` 接口
- 系统管理员可查看所有用户
- 代理商用户只能查看推荐用户
- 当前用户信息正确显示
- 金豆统计信息正确显示（代理商用户）

### 3.3 反向指标 (Counter Metrics)
- 接口调用失败率 < 1%
- 权限控制不能出现越权访问
- 页面加载时间不能超过3秒

## 4. 用户画像与用户故事

### 4.1 目标用户
1. **系统管理员**: 需要查看和管理所有用户
2. **代理商用户**: 需要查看自己推荐的用户和金豆统计
3. **开发人员**: 需要维护和优化用户管理功能

### 4.2 用户故事
- **作为系统管理员**, 我希望能够查看所有用户列表，以便进行用户管理
- **作为代理商用户**, 我希望只能查看我推荐的用户，以便跟踪我的业绩
- **作为代理商用户**, 我希望能看到我的金豆统计信息，以便了解我的收益情况

## 5. 功能规格详述

### 5.1 接口集成规格
```typescript
// 新接口返回数据结构
interface UserListResponseVO {
  records: InzUserFront[];           // 用户列表
  total: number;                     // 总数
  size: number;                      // 页面大小
  current: number;                   // 当前页
  userInfo: CurrentUserInfoVO;       // 当前用户信息
  goldenBeanStats?: GoldenBeanStatsVO; // 金豆统计（代理商用户）
}

interface CurrentUserInfoVO {
  username: string;
  phone: string;
  roleDescription: string;
  roleCodes: string[];
}

interface GoldenBeanStatsVO {
  totalGoldenBean: number;
  usedGoldenBean: number;
  remainingGoldenBean: number;
}
```

### 5.2 权限控制逻辑
1. **系统管理员**: 查看所有用户，不添加过滤条件
2. **代理商用户**: 只查看推荐用户，根据推荐关系过滤
3. **其他角色**: 无权限访问，返回错误信息

### 5.3 业务逻辑规则
- 分页查询支持：pageNo（默认1）、pageSize（默认10）
- 支持查询条件过滤
- 自动根据登录用户角色进行数据权限控制
- 代理商用户额外返回金豆统计信息

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 修改 `InzUserFront.api.ts` 中的 list 接口调用
- 更新数据结构以适配新的返回格式
- 保持现有的用户界面和交互
- 保持现有的权限控制逻辑
- 集成金豆统计显示功能

### 6.2 排除功能 (Out of Scope)
- 不修改用户界面设计
- 不改变现有的用户操作流程
- 不涉及其他模块的接口修改
- 不修改后端接口逻辑

## 7. 依赖与风险

### 7.1 内部依赖
- 现有的用户前台管理模块正常运行
- 权限控制系统正常工作
- 前端组件库和工具函数可用

### 7.2 外部依赖
- 老板提供的后端 `/list` 接口稳定可用
- 数据库中的用户角色数据完整
- 推荐关系数据准确

### 7.3 潜在风险
- **高风险**: 接口数据结构不匹配导致页面报错
- **中风险**: 权限控制逻辑变更导致越权访问
- **低风险**: 金豆统计数据显示异常

## 8. 发布初步计划

### 8.1 开发阶段
1. **技术评审**: Bob进行架构分析和技术方案设计
2. **接口集成**: Alex修改API调用和数据处理逻辑
3. **测试验证**: 使用playwright进行自动化测试
4. **文档更新**: 更新相关技术文档

### 8.2 发布计划
- **开发环境测试**: 验证接口集成和权限控制
- **预发布验证**: 确认数据显示和用户体验
- **生产环境发布**: 平滑切换到新接口

### 8.3 数据跟踪
- 监控接口调用成功率
- 跟踪用户权限控制准确性
- 验证金豆统计数据正确性

---

**文档状态**: ✅ 已完成
**下一步**: 技术架构设计和开发实施
