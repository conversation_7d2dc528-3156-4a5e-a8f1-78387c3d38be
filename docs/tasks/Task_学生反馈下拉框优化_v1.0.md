# 学生反馈下拉框优化 - 任务规划

## 任务概述
基于PRD文档，将学生反馈下拉框优化需求拆解为具体的开发任务。

## 任务列表

### Task 1: 问题诊断与分析
**负责人**: Alex  
**预估时间**: 2小时  
**优先级**: P0  

**任务描述**:
- 分析当前 `getClassStudent` API接口调用问题
- 检查数据返回格式和前端处理逻辑
- 对比用户信息行正常显示的实现方式
- 确定问题根本原因

**验收标准**:
- [ ] 明确API调用失败的具体原因
- [ ] 确认数据格式是否正确
- [ ] 生成问题诊断报告

### Task 2: API接口验证与修复
**负责人**: Alex  
**预估时间**: 3小时  
**优先级**: P0  

**任务描述**:
- 验证 `getClassStudent` 接口是否正常工作
- 检查接口参数传递是否正确
- 修复数据加载问题
- 确保返回数据格式符合前端需求

**验收标准**:
- [ ] API接口调用成功
- [ ] 返回完整的学生数据列表
- [ ] 数据格式符合前端组件要求

### Task 3: 下拉框组件优化
**负责人**: Alex  
**预估时间**: 4小时  
**优先级**: P0  

**任务描述**:
- 修复下拉框数据映射问题
- 优化 `fieldNames` 配置，确保显示学生姓名
- 实现自定义选项模板，显示详细信息
- 优化选中学生后的信息展示

**具体修改**:
```typescript
// 优化数据映射
studentOptions.value = res.result.map(item => ({
  label: item.studentName,     // 显示学生姓名
  value: item.id,             // 使用记录ID作为值
  studentName: item.studentName,
  studentPhone: item.studentPhone,
  joinTime: item.joinTime,
  feedback: item.feedback,
  studentId: item.studentId,
  ...item
}));

// 优化fieldNames配置
:fieldNames="{ label: 'studentName', value: 'id' }"
```

**验收标准**:
- [ ] 下拉框正确显示学生姓名
- [ ] 选项详情显示姓名、电话、加入时间
- [ ] 选中学生后正确显示完整信息

### Task 4: 搜索功能实现
**负责人**: Alex  
**预估时间**: 3小时  
**优先级**: P1  

**任务描述**:
- 实现可输入筛选的搜索功能
- 支持按学生姓名模糊搜索
- 支持按电话号码模糊搜索
- 优化搜索性能和用户体验

**具体实现**:
```typescript
// 优化搜索过滤函数
const filterStudentOption = (input: string, option: any) => {
  const searchText = input.toLowerCase();
  return (
    option.studentName?.toLowerCase().includes(searchText) ||
    option.studentPhone?.includes(searchText) ||
    option.label?.toLowerCase().includes(searchText)
  );
};
```

**验收标准**:
- [ ] 支持实时搜索，无需点击搜索按钮
- [ ] 搜索响应时间 < 500ms
- [ ] 搜索结果准确匹配

### Task 5: 错误处理与用户体验优化
**负责人**: Alex  
**预估时间**: 2小时  
**优先级**: P1  

**任务描述**:
- 添加完善的错误处理机制
- 优化加载状态显示
- 添加友好的错误提示
- 优化空数据状态处理

**验收标准**:
- [ ] API调用失败时显示友好错误提示
- [ ] 加载过程中显示loading状态
- [ ] 无数据时显示合适的提示信息

### Task 6: 测试与验证
**负责人**: Alex  
**预估时间**: 3小时  
**优先级**: P1  

**任务描述**:
- 编写单元测试用例
- 进行集成测试
- 验证现有功能不受影响
- 性能测试和优化

**测试用例**:
1. 学生数据正常加载测试
2. 搜索功能准确性测试
3. 错误处理机制测试
4. 性能压力测试
5. 兼容性测试

**验收标准**:
- [ ] 所有测试用例通过
- [ ] 现有功能正常工作
- [ ] 性能指标达到要求

## 技术实现要点

### 1. 数据结构优化
```typescript
interface StudentOption {
  label: string;        // 学生姓名 - 用于显示
  value: string;        // 记录ID - 用于提交
  studentName: string;  // 学生姓名
  studentPhone: string; // 学生电话
  joinTime: string;     // 加入时间
  feedback?: string;    // 当前反馈
  studentId: string;    // 学生用户ID
}
```

### 2. 组件配置优化
```vue
<a-select
  v-model:value="formState.studentId"
  placeholder="请输入学生姓名或电话搜索"
  :options="studentOptions"
  show-search
  :filter-option="filterStudentOption"
  allowClear
  @change="handleStudentChange"
  :loading="studentLoading"
  :fieldNames="{ label: 'label', value: 'value' }"
>
```

### 3. 搜索功能实现
- 使用 `show-search` 属性启用搜索
- 自定义 `filter-option` 函数实现多字段搜索
- 优化搜索算法，支持模糊匹配

## 风险控制

### 技术风险
1. **API兼容性**: 确保修改不影响其他功能
2. **性能风险**: 大量学生数据时的搜索性能
3. **数据一致性**: 确保数据映射正确

### 缓解措施
1. 充分测试现有功能
2. 实现虚拟滚动或分页
3. 添加数据验证机制

## 时间安排

| 任务 | 开始时间 | 结束时间 | 依赖关系 |
|------|----------|----------|----------|
| Task 1 | Day 1 AM | Day 1 PM | 无 |
| Task 2 | Day 1 PM | Day 2 AM | Task 1 |
| Task 3 | Day 2 AM | Day 2 PM | Task 2 |
| Task 4 | Day 2 PM | Day 3 AM | Task 3 |
| Task 5 | Day 3 AM | Day 3 PM | Task 4 |
| Task 6 | Day 3 PM | Day 4 AM | Task 5 |

**总预估时间**: 2.5天  
**关键路径**: Task 1 → Task 2 → Task 3 → Task 4 → Task 5 → Task 6
